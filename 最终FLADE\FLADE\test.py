from graphviz import Digraph

# 创建有向图对象
dot = Digraph(comment='基于差分进化与地形感知的影响力最大化算法框架图')
dot.attr(rankdir='TB', size='10,8')

# 输入数据层
dot.node('A', '输入：社交网络图 G，传播概率 p，种子集规模 k，参数设定')

# 初始化模块
dot.node('B', '初始化：\n邻居缓存 + 节点得分缓存\n种群初始化 (Degree Initialization)\nEDV初值预计算')

# EDV计算核心
dot.node('C', 'EDV适应度计算器\n(带邻居缓存 + 节点分值缓存)')

# 峰度判别模块
dot.node('D', '计算当前种群适应度\n→ 峰度统计与阈值判别')

# 局部搜索优化
dot.node('E', '地形感知策略触发\n→ 局部搜索优化 (优化最优个体)')

# 智能节点替换
dot.node('F', '停滞检测 + 汉明距离判别\n→ 智能节点替换')

# 差分进化主循环
dot.node('G', '差分变异 + 杂交 + 选择')

# 进化轨迹记录
dot.node('H', '适应度变化记录\n峰度序列记录\n节点替换记录')

# 结果输出
dot.node('I', '进化轨迹图 + 峰度曲线\n种子集最优解 + Excel导出')

# 箭头连接各模块
dot.edges([('A', 'B'),
           ('B', 'C'),
           ('C', 'D'),
           ('D', 'E'),
           ('D', 'F'),
           ('D', 'G'),
           ('E', 'G'),
           ('F', 'G'),
           ('G', 'C'),
           ('G', 'H'),
           ('H', 'D'),
           ('H', 'I')])

# 渲染输出PDF或PNG文件
dot.render('algorithm_framework', format='png', cleanup=True)
